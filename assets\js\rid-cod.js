jQuery(document).ready(function($) {
    
    // --- Helper Function: Clean Phone Number ---
    function cleanPhoneNumber(phoneNumber, countryCode) {
        if (!phoneNumber) return '';
        
        // Remove all non-numeric characters
        phoneNumber = phoneNumber.replace(/[^\d]/g, '');
        
        // Remove leading + or 00 if present
        phoneNumber = phoneNumber.replace(/^(\+|00)/, '');
        
        // Remove country codes if present at the beginning
        switch (countryCode) {
            case 'DZ':
                phoneNumber = phoneNumber.replace(/^213/, ''); // Algeria
                break;
            case 'MA':
                phoneNumber = phoneNumber.replace(/^212/, ''); // Morocco
                break;
            case 'SA':
                phoneNumber = phoneNumber.replace(/^966/, ''); // Saudi Arabia
                break;
            case 'TN':
                phoneNumber = phoneNumber.replace(/^216/, ''); // Tunisia
                break;
            case 'EG':
                phoneNumber = phoneNumber.replace(/^20/, ''); // Egypt
                break;
            case 'LY':
                phoneNumber = phoneNumber.replace(/^218/, ''); // Libya
                break;
            case 'JO':
                phoneNumber = phoneNumber.replace(/^962/, ''); // Jordan
                break;
        }
        
        return phoneNumber;
    }
    
    // --- Cache DOM Elements ---
    var form = $('#rid-cod-form');
    var productIdInput = form.find('input[name="product_id"]');
    var quantityInput = $('#rid-cod-quantity-input');
    var quantityHidden = $('#rid-cod-quantity');
    var decreaseBtn = $('#rid-cod-decrease');
    var increaseBtn = $('#rid-cod-increase');
    var countrySelect = $('#rid-cod-country'); // Country selector
    var stateSelect = $('#rid-cod-state');
    var citySelect = $('#rid-cod-city');
    var productQuantity = $('#rid-cod-product-quantity');
    var productPrice = $('#rid-cod-product-price');
    var shippingPrice = $('#rid-cod-shipping-price');
    var totalPrice = $('#rid-cod-total-price');
    var messageDiv = $('#rid-cod-message');
    var submitBtn = $('#rid-cod-submit-btn');
    var originalBtnText = submitBtn.text();
    var deliveryTypeRadios = $('input[name="delivery_type"]');
    var summaryDeliveryTypeRow = $('#rid-cod-summary-delivery-type-row');
    var summaryShippingTypeRow = $('#rid-cod-summary-shipping-type-row');
    var summaryShippingType = $('#rid-cod-summary-shipping-type');
    var summaryVariationDetails = $('#rid-cod-summary-variation-details');
    var fullNameInput = $('#rid-cod-full-name');
    var phoneInput = $('#rid-cod-phone');
    var cityTextInput = $('#rid-cod-city-text'); // Fallback
    var stateTextInput = $('#rid-cod-state-text'); // Fallback
    var stickyButtonContainer = $('#rid-cod-sticky-button-container'); // Sticky button container
    var stickySubmitBtn = $('#rid-cod-sticky-submit-btn'); // Select the sticky button with its unique ID

    // --- Country and Location Variables ---
    var currentCountry = rid_cod_params.current_country || 'algeria';
    var countryData = rid_cod_params.country_data || {};
    var phonePatterns = rid_cod_params.phone_patterns || {};

    // --- Variation Elements ---
    var variationsContainer = $('.rid-cod-variations');
    var allSwatches = variationsContainer.find('.rid-cod-swatch');
    var variationIdInput = $('#rid-cod-variation-id');
    var variationPriceInput = $('#rid-cod-variation-price');
    var variationSelects = $('.rid-cod-variation-select');

    // --- Check if variations exist ---
    var hasVariations = variationsContainer.length > 0;
    var availableVariationsData = typeof rid_cod_variations !== 'undefined' ? rid_cod_variations : {};

    // --- Handle dropdown variations mode ---
    var useDropdownVariations = rid_cod_params.use_dropdown_variations || false;
    if (useDropdownVariations) {
        $('body').addClass('rid-cod-use-dropdown-variations');
        console.log('RIDCOD: Dropdown variations mode enabled');
    }

    // --- Abandoned Cart / Server-Side Draft Logic ---
    var productId = productIdInput.val();
    var draftOrderId = null; // Store the draft order ID returned by the server
    var saveDraftTimeout = null;
    var isSavingDraft = false; // Flag to prevent concurrent saves

    // --- Prevent Copy-Paste Logic ---
    if (rid_cod_params && rid_cod_params.prevent_copy_paste === true) {
        var fieldsToBlockPaste = '#rid-cod-full-name, #rid-cod-phone, #rid-cod-state-text, #rid-cod-city-text';
        $(fieldsToBlockPaste).on('paste', function(e) {
            e.preventDefault();
        });
    }

    // --- Helper Functions ---

    // Format price with country-specific currency
    function formatPrice(price) {
        var currentCountry = rid_cod_params.current_country || 'DZ';
        var currencySymbol = 'د.ج'; // Default to Algerian Dinar
        
        // Get currency symbol based on current country
        if (rid_cod_params.country_data && rid_cod_params.country_data[currentCountry]) {
            currencySymbol = rid_cod_params.country_data[currentCountry].currency_symbol || 'د.ج';
        }
        
        price = parseFloat(price);
        if (isNaN(price)) {
            return '';
        }
        
        var formattedPrice = price.toFixed(2).replace('.', ',');
        // Add thousand separators
        formattedPrice = formattedPrice.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        
        return currencySymbol + ' ' + formattedPrice;
    }

    // Make formatPrice globally available for other scripts
    window.formatPrice = formatPrice;

    // Update currency symbol in the interface when country changes
    function updateCurrencyDisplay() {
        // Update the total price display with the new currency
        updateTotalPrice();
        
        // If there are any other price displays, update them too
        $('.rid-cod-price-display').each(function() {
            var price = $(this).data('price');
            if (price) {
                $(this).html(formatPrice(price));
            }
        });
    }

    // Show messages (popup for success, standard div for errors)
    function showMessage(message, type) {
        if (type === 'success') {
            // Create popup element
            var popup = $('<div class="rid-cod-popup"></div>');
            popup.addClass(type); // Add 'success' class
            popup.html(message);
            popup.appendTo('body');

            // Center the popup initially (optional, adjust as needed)
            popup.css({
                'left': '50%',
                'transform': 'translateX(-50%)'
            });

            // Show with fade-in effect
            popup.fadeIn(300);

            // Hide and remove after a delay
            setTimeout(function() {
                popup.fadeOut(500, function() {
                    $(this).remove();
                });
            }, 4000); // Keep visible for 4 seconds

        } else {
            // Handle errors or other messages in the original div
            messageDiv.removeClass('success error processing').addClass(type).html(message).show();
            // Scroll to the error message
            $('html, body').animate({
                scrollTop: messageDiv.offset().top - 100
            }, 500);
        }
    }

    // Debounce function
    function debounce(func, wait) {
        return function() {
            var context = this, args = arguments;
            clearTimeout(saveDraftTimeout);
            saveDraftTimeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }

    // Debounce function specifically for scroll events
    function debounceScroll(func, wait) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                func.apply(context, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // --- Server-Side Draft Saving Function ---
    var saveDraftOrderToServer = debounce(function() {
        // Basic check: only save if name and phone have some value
        if (isSavingDraft || !fullNameInput.val() || !phoneInput.val()) {
            return;
        }

        isSavingDraft = true;
        console.log('Attempting to save draft...'); // Debug log

        var formData = form.serialize() +
                       '&action=rid_cod_save_draft_order' +
                       '&nonce=' + rid_cod_params.nonce + // Use the general nonce
                       '&state_full_name=' + encodeURIComponent(stateSelect.find('option:selected').text()); // Add full state name

        // Add draft_order_id if we have one
        if (draftOrderId) {
            formData += '&draft_order_id=' + draftOrderId;
        }

        $.ajax({
            type: 'POST',
            url: rid_cod_params.ajax_url,
            data: formData,
            success: function(response) {
                if (response.success && response.data.draft_order_id) {
                    draftOrderId = response.data.draft_order_id; // Store the returned ID
                    console.log('Draft order saved/updated. ID:', draftOrderId); // Debug log
                } else {
                    console.warn('Failed to save draft order:', response.data ? response.data.message : 'Unknown error');
                    // Optionally show a non-intrusive error?
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error('AJAX error saving draft order:', textStatus, errorThrown);
            },
            complete: function() {
                isSavingDraft = false; // Allow next save attempt
            }
        });
    }, 2500); // Debounce time: 2.5 seconds after last change


    // --- Quantity Logic ---
    function updateQuantity() {
        var quantity = parseInt(quantityInput.val());
        quantityHidden.val(quantity);
        productQuantity.text(quantity);
        updateTotalPrice();
        saveDraftOrderToServer(); // Trigger draft save on quantity change
    }

    decreaseBtn.on('click', function() {
        var currentVal = parseInt(quantityInput.val());
        if (currentVal > 1) {
            quantityInput.val(currentVal - 1);
            updateQuantity();
        }
    });

    increaseBtn.on('click', function() {
        var currentVal = parseInt(quantityInput.val());
        var max = parseInt(quantityInput.attr('max')) || 99;
        if (currentVal < max) {
            quantityInput.val(currentVal + 1);
            updateQuantity();
        }
    });

    // --- Variation Selection Logic ---
    function findMatchingVariation() {
        if (!hasVariations || !Array.isArray(availableVariationsData) || availableVariationsData.length === 0) return null;

        var selectedAttributes = {};
        var allAttributesSelected = true;

        // Get selected attributes from dropdowns
        variationSelects.each(function() {
            var $select = $(this);
            var attributeName = $select.attr('name');
            var selectedValue = $select.val();

            if (!selectedValue) {
                allAttributesSelected = false;
                return false; // Break loop
            }

            selectedAttributes[attributeName] = selectedValue;
        });

        var matchingVariation = null;
        if (allAttributesSelected) {
            $.each(availableVariationsData, function(index, variation) {
                var attributesMatch = true;

                // Check if all selected attributes match this variation
                $.each(selectedAttributes, function(attrName, attrValue) {
                    var variationAttrValue = variation.attributes[attrName];

                    // If variation has a specific value for this attribute and it doesn't match selection
                    if (variationAttrValue && variationAttrValue !== '' && variationAttrValue !== attrValue) {
                        attributesMatch = false;
                        return false; // Exit inner loop
                    }
                });

                if (attributesMatch) {
                    matchingVariation = variation;
                    return false; // Exit outer loop
                }
            });
        }

        // Clear previous stock status display first
        $('#rid-cod-variation-stock-status').remove();

        if (matchingVariation && matchingVariation.is_in_stock) {
            variationIdInput.val(matchingVariation.variation_id);
            variationPriceInput.val(matchingVariation.display_price);

            // Update price display
            if (matchingVariation.price_html) {
                productPrice.html(matchingVariation.price_html);
            } else {
                productPrice.html(formatPrice(matchingVariation.display_price));
            }

            // Display stock status if available_html exists
            if (matchingVariation.availability_html) {
                // Insert the stock status HTML after the price span
                productPrice.after('<div id="rid-cod-variation-stock-status" class="rid-cod-stock-status">' + matchingVariation.availability_html + '</div>');
            }

            // Update variation details in summary
            var detailsHtml = '';
            variationSelects.each(function() {
                var $select = $(this);
                var selectedText = $select.find('option:selected').text();
                var label = $select.closest('tr').find('label').text().replace(':', '');

                if ($select.val() && selectedText) {
                    // Decode URL-encoded text to handle Arabic characters properly
                    var decodedText = '';
                    try {
                        decodedText = decodeURIComponent(selectedText);
                    } catch (e) {
                        decodedText = selectedText; // Fallback to original if decode fails
                    }
                    detailsHtml += '<span class="rid-variation-meta">' + label + ': ' + decodedText + '</span><br>';
                }
            });
            summaryVariationDetails.html(detailsHtml);
        } else {
            variationIdInput.val('');
            variationPriceInput.val('');
            productPrice.html(rid_cod_params.select_variation || 'اختر النوع');
            summaryVariationDetails.html('');
            // Ensure stock status is cleared when no variation is selected/valid
            $('#rid-cod-variation-stock-status').remove();
        }
        updateTotalPrice();
        return matchingVariation;
    }

    // Handle variation select changes
    variationSelects.on('change', function() {
        console.log('RIDCOD Main: Variation select changed');
        findMatchingVariation();
        saveDraftOrderToServer(); // Trigger draft save on variation change
    });

    // Legacy swatch support (if still used)
    allSwatches.on('click', function() {
        var $this = $(this);
        var $row = $this.closest('.rid-cod-variation-row');
        var attributeName = $row.data('attribute-name');
        var hiddenInputId = '#rid-cod-selected-' + attributeName.replace('pa_', '');
        $this.siblings('.selected').removeClass('selected');
        $this.addClass('selected');
        $(hiddenInputId).val($this.data('value'));
        findMatchingVariation();
        saveDraftOrderToServer(); // Trigger draft save on variation change
    });

    // --- Shipping and Location Logic ---
    var citiesCache = {};

    // --- Country Selection Logic ---
    if (countrySelect.length > 0) {
        countrySelect.on('change', function() {
            var selectedCountry = $(this).val();
            currentCountry = selectedCountry;
            
            // Reset state and city selections
            stateSelect.html('<option value="" disabled selected>' + (rid_cod_params.select_state || 'اختر الولاية') + '</option>').prop('disabled', false);
            
            // Handle city field based on country
            if (shouldUseCityAsTextInput(selectedCountry)) {
                // For countries other than Algeria, convert to text input
                convertCitySelectToTextInput();
            } else {
                // For Algeria, ensure it's a select dropdown
                ensureCityIsSelectDropdown();
                citySelect.html('<option value="" disabled selected>' + (rid_cod_params.select_city || 'اختر البلدية') + '</option>').prop('disabled', true);
            }
            
            // Clear cache for new country
            citiesCache = {};
            
            // Update states for new country
            if (countryData[selectedCountry] && countryData[selectedCountry].states) {
                $.each(countryData[selectedCountry].states, function(stateCode, stateName) {
                    stateSelect.append($('<option>', {
                        value: stateCode,
                        text: stateName,
                        'data-state': stateName
                    }));
                });
            }
            
            // Reset shipping and totals
            shippingPrice.text(rid_cod_params.select_state || 'اختر الولاية');
            $('#rid-cod-shipping-method').val('');
            $('#rid-cod-shipping-cost').val(0);
            updateTotalPrice();
            saveDraftOrderToServer(); // Trigger draft save on country change
            updateCurrencyDisplay(); // Update currency display on country change
        });
    }

    stateSelect.on('change', function() {
        var state = $(this).val();
        var stateName = $(this).find('option:selected').data('state');
        var shouldUseCityTextInput = shouldUseCityAsTextInput(currentCountry);
        
        // Reset shipping and totals
        shippingPrice.text(rid_cod_params.select_state || 'اختر الولاية');
        $('#rid-cod-shipping-method').val('');
        $('#rid-cod-shipping-cost').val(0);
        updateTotalPrice();
        saveDraftOrderToServer(); // Trigger draft save on state change

        if (state) {
            // Special handling for countries that use text input for cities (all except Algeria)
            if (shouldUseCityTextInput) {
                // Convert select to text input for countries other than Algeria
                convertCitySelectToTextInput();
                updateShippingMethods(state, stateName);
            } else {
                // Normal dropdown handling for Algeria
                ensureCityIsSelectDropdown();
                citySelect.html('<option value="" disabled selected>جاري التحميل...</option>');
                if (citiesCache[state]) {
                    populateCityDropdown(citiesCache[state]);
                    updateShippingMethods(state, stateName);
                } else {
                     if (rid_cod_params.cities_by_state && rid_cod_params.cities_by_state[state]) {
                         citiesCache[state] = rid_cod_params.cities_by_state[state];
                         populateCityDropdown(citiesCache[state]);
                         updateShippingMethods(state, stateName);
                     } else {
                         $.ajax({
                             type: 'POST',
                             url: rid_cod_params.ajax_url,
                             data: { action: 'rid_cod_get_cities', nonce: rid_cod_params.nonce, state: state },
                             success: function(response) {
                                 if (response.success && response.data.cities) {
                                     citiesCache[state] = response.data.cities;
                                     populateCityDropdown(response.data.cities);
                                     updateShippingMethods(state, stateName);
                                 } else {
                                     citySelect.html('<option value="" disabled selected>' + (rid_cod_params.select_city || 'اختر البلدية') + '</option>');
                                 }
                             },
                             error: function() { citySelect.html('<option value="" disabled selected>خطأ في التحميل</option>'); }
                         });
                     }
                }
            }
        } else {
            // Reset city field based on country
            if (shouldUseCityTextInput) {
                convertCitySelectToTextInput();
            } else {
                ensureCityIsSelectDropdown();
                citySelect.html('<option value="" disabled selected>' + (rid_cod_params.select_city || 'اختر البلدية') + '</option>').prop('disabled', true).trigger('change');
            }
        }
    });

    function populateCityDropdown(cities) {
        citySelect.html('<option value="" disabled selected>' + (rid_cod_params.select_city || 'اختر البلدية') + '</option>');
        $.each(cities, function(index, city) {
            citySelect.append($('<option>', { value: city, text: city }));
        });
        citySelect.prop('disabled', false);
    }

    citySelect.on('change', function() {
        updateTotalPrice();
        saveDraftOrderToServer(); // Trigger draft save on city change
    });

    function updateShippingMethods(stateCode, stateName) {
        var shippingCost = null;
        var shippingMethod = '';
        var foundShipping = false;
        var selectedDeliveryType = 'desk';
        if (rid_cod_params.enable_delivery_type && deliveryTypeRadios.length > 0) {
            selectedDeliveryType = $('input[name="delivery_type"]:checked').val() || 'desk';
        }

        if (typeof rid_cod_params.wc_shipping_methods !== 'undefined') {
            $.each(rid_cod_params.wc_shipping_methods, function(zoneName, methods) {
                $.each(methods, function(index, method) {
                    if (method.locations && Array.isArray(method.locations)) {
                        $.each(method.locations, function(i, location) {
                            if (location.type === 'state' && location.code === stateCode) {
                                shippingCost = parseFloat(method.cost);
                                shippingMethod = method.title;
                                foundShipping = true; return false;
                            }
                        });
                    }
                    if (foundShipping) return false;
                });
                if (foundShipping) return false;
            });
        }

        if (!foundShipping && typeof rid_cod_params.default_state_costs !== 'undefined') {
            var stateCodeStr = String(stateCode);
            var defaultCosts = rid_cod_params.default_state_costs[stateCodeStr];
            if (defaultCosts) {
                 if (selectedDeliveryType === 'home' && defaultCosts.home !== null && !isNaN(parseFloat(defaultCosts.home))) {
                     shippingCost = parseFloat(defaultCosts.home);
                 } else if (selectedDeliveryType === 'desk' && defaultCosts.desk !== null && !isNaN(parseFloat(defaultCosts.desk))) {
                     shippingCost = parseFloat(defaultCosts.desk);
                 } else if (defaultCosts.home !== null && !isNaN(parseFloat(defaultCosts.home))) {
                      shippingCost = parseFloat(defaultCosts.home);
                 } else if (defaultCosts.desk !== null && !isNaN(parseFloat(defaultCosts.desk))) {
                      shippingCost = parseFloat(defaultCosts.desk);
                 }
                if (shippingCost !== null) {
                    shippingMethod = rid_cod_params.shipping_text || 'سعر التوصيل';
                    foundShipping = true;
                }
            }
        }

        if (foundShipping && shippingCost !== null) {
            $('#rid-cod-shipping-method').val(shippingMethod);
            $('#rid-cod-shipping-cost').val(shippingCost);
            if (shippingCost > 0) {
                shippingPrice.html(formatPrice(shippingCost));
            } else {
                shippingPrice.html('<span class="free-shipping">' + (rid_cod_params.free_shipping || 'توصيل مجاني') + '</span>');
            }
        } else {
            $('#rid-cod-shipping-method').val('');
            $('#rid-cod-shipping-cost').val(0);
            // Show appropriate message based on whether states are shown
            if (!rid_cod_params.show_states) {
                // If states are hidden, show shipping unavailable message
                shippingPrice.html(rid_cod_params.shipping_unavailable || 'الشحن غير متوفر');
            } else {
                // If states are shown, show select state or shipping unavailable based on state selection
                shippingPrice.html(stateCode ? (rid_cod_params.shipping_unavailable || 'الشحن غير متوفر') : (rid_cod_params.select_state || 'اختر الولاية'));
            }
        }
        updateTotalPrice();
        updateSummaryShippingType();
    }

    // --- Delivery Type Logic ---
    // Use event delegation to handle delivery type changes (works for elements added dynamically)
    $(document).on('change', 'input[name="delivery_type"]', function() {
        var currentState = stateSelect.val();
        if (currentState) {
            updateShippingMethods(currentState, stateSelect.find('option:selected').data('state'));
        }
        updateSummaryShippingType();
        saveDraftOrderToServer(); // Trigger draft save on delivery type change
    });

    function updateSummaryShippingType() {
        // Handle delivery type display based on location setting
        if (rid_cod_params.enable_delivery_type) {
            var selectedType = $('input[name="delivery_type"]:checked');

            // If delivery type is in main form, we might need to sync it to summary display
            if (rid_cod_params.delivery_type_location === 'main_form') {
                // When delivery type is in main form, we don't show it in summary
                // but we might want to update any summary text if needed
                if (summaryShippingTypeRow.length) {
                    summaryShippingTypeRow.hide();
                }
            } else if (rid_cod_params.delivery_type_location === 'summary') {
                // When delivery type is in summary, show the row
                if (summaryShippingTypeRow.length) {
                    if (selectedType.length) {
                        var label = selectedType.parent('label').find('.summary-delivery-text').text().trim();
                        if (!label) {
                            // Fallback to any text in the label
                            label = selectedType.parent('label').text().trim();
                        }
                        summaryShippingType.text(label);
                        summaryShippingTypeRow.show();
                    } else {
                        summaryShippingTypeRow.hide();
                    }
                }
            }
        } else if (summaryShippingTypeRow.length) {
            summaryShippingTypeRow.hide();
        }
    }

    // --- Total Price Calculation ---
    function updateTotalPrice() {
        var quantity = parseInt(quantityInput.val()) || 1;
        var productPriceValue = 0;
        var shippingCost = parseFloat($('#rid-cod-shipping-cost').val()) || 0;
        if (hasVariations && variationIdInput.val()) {
             productPriceValue = parseFloat(variationPriceInput.val());
        } else if (!hasVariations) {
             if (typeof rid_cod_params.base_product_price !== 'undefined') {
                  productPriceValue = parseFloat(rid_cod_params.base_product_price);
             }
        }
        if (isNaN(productPriceValue)) { productPriceValue = 0; }
        var total = (productPriceValue * quantity) + shippingCost;
        if (total > 0) {
            totalPrice.html(formatPrice(total));
        } else if (hasVariations && !variationIdInput.val()) {
            totalPrice.html(rid_cod_params.select_variation || 'اختر النوع');
        } else if (rid_cod_params.show_states && !rid_cod_params.force_text_fields && !stateSelect.val()) {
             totalPrice.html(rid_cod_params.select_state || 'اختر الولاية');
        } else {
            totalPrice.html(formatPrice(0));
        }
    }

    // --- Sticky Button Logic ---
    // --- Sticky Button Logic ---
    function handleStickyButtonVisibility() {
        var summaryWrapper = $('#rid-cod-summary-wrapper'); // Get the order summary wrapper
        if (!stickyButtonContainer.length || !summaryWrapper.length) {
            return; // Exit if elements don't exist
        }

        var scrollPosition = $(window).scrollTop();
        var summaryTop = summaryWrapper.offset().top;
        // Optional: Add a small buffer so it appears slightly before reaching the exact top
        var triggerPoint = summaryTop - 50; // Show 50px before reaching the summary

        // Show sticky button when scrolled down to the summary section (or slightly before)
        if (scrollPosition >= triggerPoint) {
             if (!stickyButtonContainer.hasClass('visible')) {
                 stickyButtonContainer.addClass('visible');
             }
        } else {
             // Hide sticky button if scrolled back up above the trigger point
             if (stickyButtonContainer.hasClass('visible')) {
                 stickyButtonContainer.removeClass('visible');
             }
        }
    }

    // Check if the sticky button feature is enabled via localized parameters
    // Compare with boolean true instead of string 'yes'
    if (typeof rid_cod_params !== 'undefined' && rid_cod_params.enable_sticky_button === true && stickyButtonContainer.length && stickySubmitBtn.length && submitBtn.length > 0) {

        // Debounced scroll handler
        var debouncedScrollHandler = debounceScroll(handleStickyButtonVisibility, 100); // Debounce scroll checks

        // Attach scroll listener
        $(window).on('scroll', debouncedScrollHandler);

        // Initial check in case the page loads scrolled down
        handleStickyButtonVisibility();

        // Add click handler for the sticky button to trigger the main form submit
        stickySubmitBtn.on('click', function(e) {
            e.preventDefault(); // Prevent default if it's a button/link
            // Ensure the original button exists and trigger its click
            if (submitBtn.length) {
                submitBtn.trigger('click'); // Trigger click on the *original* submit button
            } else {
                // Fallback: directly submit the form if original button isn't found (less ideal)
                form.trigger('submit');
            }
            // Removed incorrect else block from here
        });
    } else { // Correct placement for the else block
         // console.log('// Debug: Sticky button condition not met or feature disabled.'); // Removed debug log
    }

    // --- Helper function to decode URL-encoded text (for Arabic labels) ---
    function decodeText(text) {
        if (!text) return text;
        
        try {
            var decoded = text;
            
            // Special handling for pa_ prefixed attributes
            if (decoded.indexOf('pa_%') === 0) {
                // Remove pa_ prefix first
                decoded = decoded.substring(3);
            }
            
            // Keep decoding while there are still encoded characters
            var maxIterations = 5; // Prevent infinite loops
            var iterations = 0;
            while (decoded.indexOf('%') !== -1 && decoded !== decodeURIComponent(decoded) && iterations < maxIterations) {
                var beforeDecode = decoded;
                decoded = decodeURIComponent(decoded);
                iterations++;
                
                // Break if no change (prevent infinite loop)
                if (beforeDecode === decoded) {
                    break;
                }
            }
            
            // Remove other common attribute prefixes
            var prefixes = ['attribute_pa_', 'attribute_'];
            for (var i = 0; i < prefixes.length; i++) {
                if (decoded.indexOf(prefixes[i]) === 0) {
                    decoded = decoded.substring(prefixes[i].length);
                    break;
                }
            }
            
            return decoded.trim();
        } catch (e) {
            console.warn('RID COD: Failed to decode text:', text, e);
            return text;
        }
    }

    // --- Form Submission ---
    form.on('submit', function(e) {
        e.preventDefault();

        // Validation
        var allAttributesSelected = true;
        var missingAttributeLabel = '';
        if (hasVariations) {
            // Check if all variation selects have values
            variationSelects.each(function() {
                var $select = $(this);
                if (!$select.val()) {
                    allAttributesSelected = false;
                    // Try to get decoded label from data attribute first
                    var decodedLabel = $select.data('decoded-label');
                    if (!decodedLabel) {
                        // Fallback to getting from closest label and decoding
                        var rawLabel = $select.closest('tr').find('label').text().replace(':', '');
                        decodedLabel = decodeText(rawLabel);
                    }
                    missingAttributeLabel = decodedLabel;
                    return false;
                }
            });

            // Also check legacy swatches if they exist
            if (allAttributesSelected && allSwatches.length > 0) {
                variationsContainer.find('.rid-cod-variation-row').each(function() {
                    if (!$(this).find('.rid-cod-swatch.selected').length) {
                        allAttributesSelected = false;
                        var rawLabel = $(this).find('.rid-cod-variation-label').text().replace(':', '');
                        missingAttributeLabel = decodeText(rawLabel);
                        return false;
                    }
                });
            }

            if (!allAttributesSelected) {
                showMessage('الرجاء اختيار ' + missingAttributeLabel, 'error');
                $('html, body').animate({ scrollTop: variationsContainer.offset().top - 100 }, 500);
                return;
            }
            if (!variationIdInput.val()) {
                 showMessage('هذا الخيار غير متوفر حالياً.', 'error');
                 $('html, body').animate({ scrollTop: variationsContainer.offset().top - 100 }, 500);
                 return;
            }
        }
        // Only validate state if states are shown
        if (rid_cod_params.show_states && stateSelect.length && !stateSelect.val()) {
             showMessage('الرجاء اختيار الولاية', 'error');
             stateSelect.focus(); return;
        }
        // Only validate city if cities are shown
        if (rid_cod_params.show_cities) {
            // Get current city element (could be select or input)
            var currentCityElement = $('#rid-cod-city');

            if (currentCityElement.length) {
                var cityValue = currentCityElement.val();
                var isDisabled = currentCityElement.prop('disabled');

                // Check if city field is enabled and empty
                if (!isDisabled && (!cityValue || cityValue.trim() === '')) {
                    showMessage('الرجاء اختيار البلدية', 'error');
                    currentCityElement.focus();
                    return;
                }
            }
        }
        var phoneInputVal = $('#rid-cod-phone');
        var phoneNumber = phoneInputVal.val().trim();
        
        if (rid_cod_params.enable_phone_validation) {
            // Clean phone number using helper function
            phoneNumber = cleanPhoneNumber(phoneNumber, currentCountry);
            
            // Get current country's phone pattern
            var currentPattern = phonePatterns[currentCountry];
            // Get country name safely
            var countryName = currentCountry; // Default to country code
            if (countryData[currentCountry] && countryData[currentCountry].name) {
                countryName = countryData[currentCountry].name;
            }
            
            if (currentPattern) {
                // Remove the regex delimiters and flags if present
                var pattern = currentPattern.replace(/^\/|\/[gimuy]*$/g, '');
                var phoneRegex = new RegExp(pattern);
                if (!phoneRegex.test(phoneNumber)) {
                    var errorMessage = 'الرجاء إدخال رقم هاتف ' + countryName + ' صحيح';
                    // Add phone example if available
                    if (countryData[currentCountry] && countryData[currentCountry].phone_example) {
                        errorMessage += '. مثال: ' + countryData[currentCountry].phone_example;
                    }
                    showMessage(errorMessage, 'error');
                    phoneInputVal.focus(); return;
                }
            }
        }

        submitBtn.prop('disabled', true).text(rid_cod_params.processing || 'جاري المعالجة...');
        messageDiv.hide();        // Clear any pending draft save timeout before final submission
        clearTimeout(saveDraftTimeout);

        // Enhanced form data preparation with variation attributes
        var formData = form.serialize() + '&action=rid_cod_place_order' + '&state_full_name=' + encodeURIComponent(stateSelect.find('option:selected').text());

        // Add variation attributes explicitly if they exist
        if (hasVariations && variationSelects.length > 0) {
            variationSelects.each(function() {
                var $select = $(this);
                var attrName = $select.attr('name');
                var attrValue = $select.val();
                
                if (attrValue) {
                    // Ensure attribute is included in form data (may already be in serialize())
                    if (formData.indexOf(attrName + '=') === -1) {
                        formData += '&' + encodeURIComponent(attrName) + '=' + encodeURIComponent(attrValue);
                    }
                }
            });
            
            // Log for debugging
            console.log('RIDCOD: Final form data includes variation attributes');
        }

        $.ajax({
            type: 'POST',
            url: rid_cod_params.ajax_url,
            // IMPORTANT: We are NOT sending draft_order_id here.
            // process_order should always create a NEW final order.
            data: formData,
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message || 'تم الطلب بنجاح', 'success'); // Update client-side fallback to match new default
                    draftOrderId = null; // Clear the draft ID on successful final submission
                    form[0].reset();
                    quantityInput.val(1);
                    updateQuantity(); // Call AFTER resetting quantity
                    stateSelect.val('').trigger('change');
                    allSwatches.removeClass('selected');
                    variationsContainer.find('input[type="hidden"]').val('');
                    findMatchingVariation();
                    if (response.data.redirect) {
                        setTimeout(function() { window.location.href = response.data.redirect; }, 2000);
                    } else {
                         submitBtn.prop('disabled', false).text(originalBtnText);
                    }
                } else {
                    var errorMessage = response.data.message || rid_cod_params.error || 'حدث خطأ.';
                    // Decode error message in case it contains encoded Arabic text
                    errorMessage = decodeText(errorMessage);
                    showMessage(errorMessage, 'error');
                    submitBtn.prop('disabled', false).text(originalBtnText);
                }
            },
            error: function(xhr, status, error) {
                var errorMessage = rid_cod_params.error || 'حدث خطأ في الشبكة. يرجى المحاولة مرة أخرى.';
                // Try to extract server error message if available
                if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                    errorMessage = decodeText(xhr.responseJSON.data.message);
                }
                showMessage(errorMessage, 'error');
                submitBtn.prop('disabled', false).text(originalBtnText);
            }
        });
    });

    // --- Order Summary Toggle ---
    var summaryHeader = $('#rid-cod-summary-header');
    var summaryContent = $('#rid-cod-summary-content');
    var summaryToggle = $('#rid-cod-summary-toggle');
    var startCollapsed = rid_cod_params && rid_cod_params.summary_start_collapsed === true;
    if (startCollapsed) {
        summaryContent.hide();
        summaryToggle.removeClass('rid-icon-arrow-up').addClass('rid-icon-arrow-down');
    } else {
        summaryContent.show();
        summaryToggle.removeClass('rid-icon-arrow-down').addClass('rid-icon-arrow-up');
    }
    summaryHeader.on('click', function() {
        summaryContent.slideToggle(300);
        summaryToggle.toggleClass('rid-icon-arrow-down rid-icon-arrow-up');
    });

    // --- Initial Calculations & Event Listeners for Draft Saving ---
    if (stateSelect.length && stateSelect.val()) {
         stateSelect.trigger('change'); // Trigger initial calculation if state pre-selected
    }

    // Initial update for summary shipping type if enabled
    updateSummaryShippingType();

    // Initial update for total price
    updateTotalPrice();

    // Initialize delivery type functionality if enabled
    if (rid_cod_params.enable_delivery_type) {
        // Ensure delivery type changes trigger shipping updates
        setTimeout(function() {
            var currentState = stateSelect.val();
            if (currentState) {
                updateShippingMethods(currentState, stateSelect.find('option:selected').data('state'));
            }
        }, 100);
    }

    // Trigger draft save on input changes (debounced)
    form.find('input[type="text"], input[type="tel"], input[type="email"], select, textarea').on('input change', saveDraftOrderToServer);
    // Use event delegation for delivery type changes (works for dynamically added elements)
    $(document).on('change', 'input[name="delivery_type"]', saveDraftOrderToServer);

    // --- Helper functions for city field handling ---
    
    // Determine if country should use text input for cities (all countries except Algeria)
    function shouldUseCityAsTextInput(country) {
        // If states are hidden, always use text input for cities (even for Algeria)
        if (!rid_cod_params.show_states) {
            return true;
        }
        // Algeria (DZ) should use dropdown for cities, all others use text input
        return country !== 'DZ';
    }
    
    function convertCitySelectToTextInput() {
        var currentValue = citySelect.val() || '';
        var container = citySelect.parent();
        var placeholder = rid_cod_params.select_city || 'اختر البلدية';
        
        // Remove existing select
        citySelect.remove();
        
        // Create text input with same ID and classes
        var textInput = $('<input>', {
            type: 'text',
            id: 'rid-cod-city',
            name: 'city',
            class: citySelect.attr('class'),
            placeholder: placeholder,
            value: currentValue,
            required: rid_cod_params.show_cities
        });
        
        // Add to container
        container.append(textInput);
        
        // Update citySelect reference
        citySelect = textInput;
        
        // Add change event for draft saving
        textInput.on('input change', function() {
            updateTotalPrice();
            saveDraftOrderToServer();
        });
        
        textInput.prop('disabled', false);
    }
    
    function ensureCityIsSelectDropdown() {
        var currentElement = $('#rid-cod-city');
        
        // If it's already a select, do nothing
        if (currentElement.is('select')) {
            citySelect = currentElement;
            return;
        }
        
        // If it's an input, convert to select
        if (currentElement.is('input')) {
            var currentValue = currentElement.val() || '';
            var container = currentElement.parent();
            var classNames = currentElement.attr('class');
            
            // Remove existing input
            currentElement.remove();
            
            // Create select dropdown
            var selectElement = $('<select>', {
                id: 'rid-cod-city',
                name: 'city',
                class: classNames,
                required: rid_cod_params.show_cities
            });
            
            // Add default option
            selectElement.html('<option value="" disabled selected>' + (rid_cod_params.select_city || 'اختر البلدية') + '</option>');
            
            // Add to container
            container.append(selectElement);
            
            // Update citySelect reference
            citySelect = selectElement;
            
            // Re-attach change event
            citySelect.on('change', function() {
                updateTotalPrice();
                saveDraftOrderToServer();
            });
            
            citySelect.prop('disabled', true);
        }
    }

    function convertStateSelectToTextInput() {
        var currentElement = $('#rid-cod-state');

        // If it's already a text input, do nothing
        if (currentElement.is('input[type="text"]')) {
            stateSelect = currentElement;
            return;
        }

        // If it's a select, convert to text input
        if (currentElement.is('select')) {
            var currentValue = currentElement.val() || '';
            var container = currentElement.parent();
            var classNames = currentElement.attr('class');
            var placeholder = rid_cod_params.select_state || 'الولاية';

            // Remove existing select
            currentElement.remove();

            // Create text input with same ID and classes
            var textInput = $('<input>', {
                type: 'text',
                id: 'rid-cod-state-text',
                name: 'state',
                class: classNames,
                placeholder: placeholder,
                value: currentValue,
                required: rid_cod_params.show_states
            });

            // Add to container
            container.append(textInput);

            // Update stateSelect reference
            stateSelect = textInput;

            // Add change event for draft saving
            textInput.on('input change', function() {
                updateTotalPrice();
                saveDraftOrderToServer();
            });

            textInput.prop('disabled', false);
        }
    }

    // --- Initialize city field based on current country ---
    function initializeCityFieldForCountry() {
        var initialCountry = rid_cod_params.current_country || currentCountry;
        if (shouldUseCityAsTextInput(initialCountry)) {
            // For countries other than Algeria, convert to text input
            convertCitySelectToTextInput();
        } else {
            // For Algeria, ensure it's a select dropdown
            ensureCityIsSelectDropdown();
        }
    }

    // Initial call to set up city field correctly
    initializeCityFieldForCountry();

    // --- Form Control Features ---

    // Apply variation size class (default to medium if not set)
    var variationSize = rid_cod_params.variation_size || 'medium';
    var sizeClass = 'rid-variation-size-' + variationSize;
    $('#rid-cod-checkout').addClass(sizeClass);

    // Apply color names display
    if (rid_cod_params.show_color_names) {
        $('#rid-cod-checkout').addClass('rid-show-color-names');
    } else {
        $('#rid-cod-checkout').removeClass('rid-show-color-names');
    }

    // Handle states/cities visibility
    if (!rid_cod_params.show_states) {
        // Hide state fields and use general shipping costs
        $('#rid-cod-state, #rid-cod-state-text').closest('.rid-cod-field-group').hide();

        // Convert city field to text input (even for Algeria when states are hidden)
        if (rid_cod_params.show_cities) {
            convertCitySelectToTextInput();
        }

        // Update shipping calculation to use general costs
        function updateShippingForGeneralCosts() {
            var deliveryType = $('input[name="delivery_type"]:checked').val() || 'home';
            var shippingCost = 0;

            if (rid_cod_params.enable_delivery_type) {
                shippingCost = deliveryType === 'desk' ?
                    rid_cod_params.general_shipping_desk :
                    rid_cod_params.general_shipping_home;
            } else {
                shippingCost = rid_cod_params.general_shipping_home;
            }

            $('#rid-cod-shipping-cost').val(shippingCost);

            // Update shipping price display in summary
            if (shippingCost > 0) {
                shippingPrice.html(formatPrice(shippingCost));
            } else {
                shippingPrice.html('<span class="free-shipping">' + (rid_cod_params.free_shipping || 'توصيل مجاني') + '</span>');
            }

            // Update summary shipping type display
            updateSummaryShippingType();

            updateTotalPrice();
        }

        // Override state change handler
        stateSelect.off('change').on('change', updateShippingForGeneralCosts);
        // Use event delegation for delivery type changes
        $(document).off('change', 'input[name="delivery_type"]').on('change', 'input[name="delivery_type"]', updateShippingForGeneralCosts);

        // Initial calculation
        updateShippingForGeneralCosts();
    }

    if (!rid_cod_params.show_cities) {
        // Hide city fields
        $('#rid-cod-city, #rid-cod-city-text').closest('.rid-cod-field-group').hide();
    }

    // Handle force text fields mode
    if (rid_cod_params.force_text_fields) {
        // Convert any existing select fields to text inputs
        convertStateSelectToTextInput();
        convertCitySelectToTextInput();

        // Use general shipping costs (same logic as when states are hidden)
        function updateShippingForTextFields() {
            var deliveryType = $('input[name="delivery_type"]:checked').val() || 'home';
            var shippingCost = 0;

            if (rid_cod_params.enable_delivery_type) {
                shippingCost = deliveryType === 'desk' ?
                    rid_cod_params.general_shipping_desk :
                    rid_cod_params.general_shipping_home;
            } else {
                shippingCost = rid_cod_params.general_shipping_home;
            }

            $('#rid-cod-shipping-cost').val(shippingCost);

            // Update shipping price display in summary
            if (shippingCost > 0) {
                shippingPrice.html(formatPrice(shippingCost));
            } else {
                shippingPrice.html('<span class="free-shipping">' + (rid_cod_params.free_shipping || 'توصيل مجاني') + '</span>');
            }

            // Update summary shipping type display
            updateSummaryShippingType();

            updateTotalPrice();
        }

        // Override state and city change handlers to use general costs
        $(document).off('change input', '#rid-cod-state-text, #rid-cod-city-text').on('change input', '#rid-cod-state-text, #rid-cod-city-text', updateShippingForTextFields);
        // Use event delegation for delivery type changes
        $(document).off('change', 'input[name="delivery_type"]').on('change', 'input[name="delivery_type"]', updateShippingForTextFields);

        // Initial calculation
        updateShippingForTextFields();

        // Also trigger initial update for total price to ensure it doesn't show "select state"
        setTimeout(function() {
            updateTotalPrice();
        }, 100);
    }

    // --- Form Style JavaScript Enhancements ---

    // Add interactive effects for modern and mobile styles
    if ($('.rid-cod-form-modern').length || $('.rid-cod-form-mobile').length) {

        // Enhanced input focus effects
        $('.rid-cod-field-group input, .rid-cod-field-group select, .rid-cod-field-group textarea').on('focus', function() {
            $(this).closest('.rid-cod-field-group').addClass('focused');
        }).on('blur', function() {
            $(this).closest('.rid-cod-field-group').removeClass('focused');
        });

        // Floating label effect disabled for modern style to prevent placeholder issues
        // The modern form now uses simple placeholders for better UX
        if ($('.rid-cod-form-modern').length) {
            // Modern form uses standard placeholders - no floating labels needed
            // This prevents the placeholder disappearing issue
        }

        // Enhanced button animations
        $('#rid-cod-submit-btn').on('mousedown', function() {
            $(this).addClass('pressed');
        }).on('mouseup mouseleave', function() {
            $(this).removeClass('pressed');
        });

        // Quantity button enhancements
        $('.rid-cod-quantity-selector button').on('click', function() {
            $(this).addClass('clicked');
            setTimeout(() => {
                $(this).removeClass('clicked');
            }, 150);
        });
    }

    // --- Enhanced Validation for Notes Field ---
    if ($('#rid-cod-notes').length) {
        $('#rid-cod-notes').on('input', function() {
            var maxLength = 500; // Set maximum length for notes
            var currentLength = $(this).val().length;

            if (currentLength > maxLength) {
                $(this).val($(this).val().substring(0, maxLength));
            }

            // Add character counter if needed
            var counter = $(this).siblings('.character-counter');
            if (counter.length === 0) {
                counter = $('<div class="character-counter"></div>');
                $(this).after(counter);
            }
            counter.text($(this).val().length + '/' + maxLength);
        });
    }

    // --- Initialize Delivery Type Location Handling ---
    function initializeDeliveryTypeLocation() {
        // Ensure delivery type options work correctly based on location
        if (rid_cod_params.enable_delivery_type) {
            // Add visual feedback for delivery type selection
            $(document).on('change', 'input[name="delivery_type"]', function() {
                // Update visual state of delivery options
                var $option = $(this).closest('.delivery-option, .summary-delivery-option');
                var $allOptions = $('input[name="delivery_type"]').closest('.delivery-option, .summary-delivery-option');

                // Remove selected class from all options
                $allOptions.removeClass('selected');

                // Add selected class to current option
                $option.addClass('selected');
            });

            // Initialize the selected state on page load
            var $checkedOption = $('input[name="delivery_type"]:checked');
            if ($checkedOption.length) {
                $checkedOption.closest('.delivery-option, .summary-delivery-option').addClass('selected');
            }
        }
    }

    // Initialize delivery type location handling
    initializeDeliveryTypeLocation();

}); // End of main document ready function

// Fix for delivery section overlay issue
function fixDeliverySection() {
        var deliverySection = $('.rid-cod-delivery-section');
        var deliveryOptions = $('.rid-delivery-options .delivery-option');
        
        if (deliverySection.length > 0) {
            // Force remove any overlays
            deliverySection.css({
                'position': 'relative',
                'z-index': '999999',
                'pointer-events': 'auto',
                'isolation': 'isolate'
            });
            
            // Make delivery options clickable
            deliveryOptions.each(function() {
                var $option = $(this);
                var $radio = $option.find('input[type="radio"]');
                
                // Remove any existing click handlers
                $option.off('click.delivery');
                
                // Add new click handler
                $option.on('click.delivery', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Uncheck all other radios
                    $('input[name="delivery_type"]').prop('checked', false);
                    $('.delivery-option').removeClass('selected');
                    
                    // Check this radio
                    $radio.prop('checked', true);
                    $option.addClass('selected');
                    
                    // Trigger change event
                    $radio.trigger('change');
                    
                    console.log('Delivery option selected:', $radio.val());
                });
                
                // Also handle direct radio click
                $radio.on('click.delivery', function(e) {
                    e.stopPropagation();
                    $(this).closest('.delivery-option').trigger('click.delivery');
                });
            });
        }
    }
    
    // Call the fix function when document is ready
    $(document).ready(function() {
        setTimeout(fixDeliverySection, 100);
        
        // Also call it after any dynamic content changes
        $(document).on('DOMNodeInserted', function() {
            setTimeout(fixDeliverySection, 50);
        });
    });
   
 // --- Third Form Style Specific JavaScript ---
    if ($('.rid-cod-form-third').length > 0) {
        console.log('RIDCOD: Third form style detected, initializing specific functionality');
        
        // Third form specific elements
        var thirdFormQuantityDisplay = $('#quantity-display-third');
        var thirdFormIncreaseBtn = $('#increase-qty-third');
        var thirdFormDecreaseBtn = $('#decrease-qty-third');
        var thirdFormSubmitBtn = $('#rid-cod-submit-btn-third');
        var thirdFormWhatsAppBtn = $('#rid-cod-whatsapp-btn-third');
        var thirdFormShippingPrice = $('#rid-cod-shipping-price-third');
        var thirdFormProductPrice = $('#rid-cod-product-price-third');
        var thirdFormTotalPrice = $('#rid-cod-total-price-third');
        
        // Initialize quantity display for third form
        var currentQuantity = 1;
        thirdFormQuantityDisplay.text(currentQuantity);
        
        // Quantity controls for third form
        thirdFormIncreaseBtn.on('click', function() {
            currentQuantity++;
            thirdFormQuantityDisplay.text(currentQuantity);
            quantityHidden.val(currentQuantity);
            updateThirdFormPrices();
        });
        
        thirdFormDecreaseBtn.on('click', function() {
            if (currentQuantity > 1) {
                currentQuantity--;
                thirdFormQuantityDisplay.text(currentQuantity);
                quantityHidden.val(currentQuantity);
                updateThirdFormPrices();
            }
        });
        
        // Update prices for third form - sync with main system
        function updateThirdFormPrices() {
            // First, update the main quantity input to sync with third form
            quantityInput.val(currentQuantity);

            // Call the main updateTotalPrice function to calculate everything
            updateTotalPrice();

            // Now sync the third form displays with the main system
            var originalShippingText = shippingPrice.html();
            var originalProductText = productPrice.html();
            var originalTotalText = totalPrice.html();

            // Update third form displays
            thirdFormShippingPrice.html(originalShippingText);
            thirdFormProductPrice.html(originalProductText);
            thirdFormTotalPrice.html(originalTotalText);
        }
        
        // Initialize prices on load
        updateThirdFormPrices();

        // Also update prices after a short delay to ensure all elements are loaded
        setTimeout(function() {
            updateThirdFormPrices();
        }, 500);
        
        // Listen for changes in the main system and sync with third form
        // The main system already handles all the events, we just need to sync

        // Use MutationObserver to watch for changes in main price elements
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    // Sync third form displays with main system
                    var originalShippingText = shippingPrice.html();
                    var originalProductText = productPrice.html();
                    var originalTotalText = totalPrice.html();

                    thirdFormShippingPrice.html(originalShippingText);
                    thirdFormProductPrice.html(originalProductText);
                    thirdFormTotalPrice.html(originalTotalText);
                }
            });
        });

        // Observe changes in main price elements
        if (shippingPrice.length) observer.observe(shippingPrice[0], { childList: true, subtree: true, characterData: true });
        if (productPrice.length) observer.observe(productPrice[0], { childList: true, subtree: true, characterData: true });
        if (totalPrice.length) observer.observe(totalPrice[0], { childList: true, subtree: true, characterData: true });
        
        // Third form submit handler
        thirdFormSubmitBtn.on('click', function(e) {
            e.preventDefault();
            
            // Validate required fields
            var fullName = $('#rid-cod-full-name').val().trim();
            var phone = $('#rid-cod-phone').val().trim();
            var address = $('#rid-cod-address').val().trim();
            
            if (!fullName) {
                showMessage('يرجى إدخال الاسم الكامل', 'error');
                $('#rid-cod-full-name').focus();
                return;
            }
            
            if (!phone) {
                showMessage('يرجى إدخال رقم الهاتف', 'error');
                $('#rid-cod-phone').focus();
                return;
            }
            
            if (!address) {
                showMessage('يرجى إدخال العنوان الكامل', 'error');
                $('#rid-cod-address').focus();
                return;
            }
            
            // Validate state selection if enabled
            if (rid_cod_params.show_states && !rid_cod_params.force_text_fields) {
                var selectedState = stateSelect.val();
                if (!selectedState) {
                    showMessage('يرجى اختيار الولاية', 'error');
                    stateSelect.focus();
                    return;
                }
            }
            
            // Validate variation selection if product has variations
            if (thirdFormHasVariations) {
                var hasSelectedVariation = false;
                thirdFormVariationSelects.each(function() {
                    if ($(this).val()) {
                        hasSelectedVariation = true;
                        return false;
                    }
                });

                if (!hasSelectedVariation) {
                    showMessage('يرجى اختيار خصائص المنتج', 'error');
                    return;
                }
            }
            
            // Update button state
            thirdFormSubmitBtn.prop('disabled', true).text(rid_cod_params.processing || 'جاري المعالجة...');
            
            // Submit the form using the main form submission logic
            form.trigger('submit');
        });
        
        // Third form WhatsApp handler
        thirdFormWhatsAppBtn.on('click', function() {
            var fullName = $('#rid-cod-full-name').val().trim();
            var phone = $('#rid-cod-phone').val().trim();
            var address = $('#rid-cod-address').val().trim();
            var state = stateSelect.find('option:selected').text() || $('#rid-cod-state-text').val();
            var city = citySelect.find('option:selected').text() || $('#rid-cod-city-text').val();
            var notes = $('#rid-cod-notes-third').val().trim();
            var deliveryType = $('input[name="delivery_type"]:checked').val();
            var deliveryTypeText = deliveryType === 'home' ? 'للمنزل' : 'لمكتب التوصيل';
            
            // Get product info
            var productName = $('.product_title').text() || 'المنتج';
            var productPrice = thirdFormProductPrice.text();
            var shippingPrice = thirdFormShippingPrice.text();
            var totalPrice = thirdFormTotalPrice.text();
            
            // Build WhatsApp message
            var message = 'طلب جديد:\n\n';
            message += '👤 الاسم: ' + fullName + '\n';
            message += '📱 الهاتف: ' + phone + '\n';
            message += '📍 العنوان: ' + address + '\n';
            if (state) message += '🏛️ الولاية: ' + state + '\n';
            if (city) message += '🏘️ البلدية: ' + city + '\n';
            message += '🚚 نوع التوصيل: ' + deliveryTypeText + '\n';
            message += '\n📦 تفاصيل الطلب:\n';
            message += '🛍️ المنتج: ' + productName + '\n';
            message += '📊 الكمية: ' + currentQuantity + '\n';
            
            // Add variation details if available
            if (thirdFormHasVariations) {
                var variationDetails = '';
                thirdFormVariationSelects.each(function() {
                    var $select = $(this);
                    var selectedText = $select.find('option:selected').text();
                    var label = $select.data('decoded-label') || $select.attr('data-attribute_name');

                    if ($select.val() && selectedText) {
                        variationDetails += label + ': ' + selectedText + '\n';
                    }
                });
                if (variationDetails) {
                    message += variationDetails;
                }
            }
            
            message += '\n💰 الأسعار:\n';
            message += '🛍️ سعر المنتجات: ' + productPrice + '\n';
            message += '🚚 سعر التوصيل: ' + shippingPrice + '\n';
            message += '💳 المجموع: ' + totalPrice + '\n';
            
            if (notes) {
                message += '\n📝 ملاحظات: ' + notes + '\n';
            }
            
            // Open WhatsApp
            var whatsappNumber = rid_cod_params.whatsapp_number || '';
            if (whatsappNumber) {
                var whatsappUrl = 'https://wa.me/' + whatsappNumber + '?text=' + encodeURIComponent(message);
                window.open(whatsappUrl, '_blank');
            }
        });
        
        // Promo code button (placeholder functionality)
        $('.rid-cod-promo-btn').on('click', function() {
            showMessage('ميزة الرمز الترويجي قيد التطوير', 'error');
        });
    } 
   // --- Third Form Style Specific JavaScript (Updated) ---
    if ($('.rid-cod-form-third').length > 0) {
        console.log('RIDCOD: Third form style detected, initializing specific functionality');
        
        // Third form specific elements
        var thirdFormQuantityDisplay = $('#quantity-display-third');
        var thirdFormIncreaseBtn = $('#increase-qty-third');
        var thirdFormDecreaseBtn = $('#decrease-qty-third');
        var thirdFormSubmitBtn = $('#rid-cod-submit-btn-third');
        var thirdFormWhatsAppBtn = $('#rid-cod-whatsapp-btn-third');
        var thirdFormShippingPrice = $('#rid-cod-shipping-price-third');
        var thirdFormProductPrice = $('#rid-cod-product-price-third');
        var thirdFormTotalPrice = $('#rid-cod-total-price-third');
        
        // Initialize quantity display for third form
        var currentQuantity = 1;
        thirdFormQuantityDisplay.text(currentQuantity);
        
        // Quantity controls for third form
        thirdFormIncreaseBtn.on('click', function() {
            currentQuantity++;
            thirdFormQuantityDisplay.text(currentQuantity);
            quantityInput.val(currentQuantity);
            quantityHidden.val(currentQuantity);
            productQuantity.text(currentQuantity);
            updateThirdFormPrices();
        });

        thirdFormDecreaseBtn.on('click', function() {
            if (currentQuantity > 1) {
                currentQuantity--;
                thirdFormQuantityDisplay.text(currentQuantity);
                quantityInput.val(currentQuantity);
                quantityHidden.val(currentQuantity);
                productQuantity.text(currentQuantity);
                updateThirdFormPrices();
            }
        });
        
        // Update prices for third form - sync with original system
        function updateThirdFormPrices() {
            // Sync with original shipping price
            var originalShippingText = shippingPrice.html();
            thirdFormShippingPrice.html(originalShippingText);
            
            // Sync with original product price
            var originalProductText = productPrice.html();
            thirdFormProductPrice.html(originalProductText);
            
            // Sync with original total price
            var originalTotalText = totalPrice.html();
            thirdFormTotalPrice.html(originalTotalText);
        }
        
        // Initialize prices on load
        updateThirdFormPrices();
        
        // Update third form prices when original prices change
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    updateThirdFormPrices();
                }
            });
        });
        
        // Observe changes in original price elements
        if (shippingPrice.length) observer.observe(shippingPrice[0], { childList: true, subtree: true, characterData: true });
        if (productPrice.length) observer.observe(productPrice[0], { childList: true, subtree: true, characterData: true });
        if (totalPrice.length) observer.observe(totalPrice[0], { childList: true, subtree: true, characterData: true });
        
        // Third form submit handler
        thirdFormSubmitBtn.on('click', function(e) {
            e.preventDefault();
            
            // Validate required fields
            var fullName = $('#rid-cod-full-name').val().trim();
            var phone = $('#rid-cod-phone').val().trim();
            var address = $('#rid-cod-address').val().trim();
            
            if (!fullName) {
                showMessage('يرجى إدخال الاسم الكامل', 'error');
                $('#rid-cod-full-name').focus();
                return;
            }
            
            if (!phone) {
                showMessage('يرجى إدخال رقم الهاتف', 'error');
                $('#rid-cod-phone').focus();
                return;
            }
            
            if (!address) {
                showMessage('يرجى إدخال العنوان الكامل', 'error');
                $('#rid-cod-address').focus();
                return;
            }
            
            // Validate state selection if enabled
            if (rid_cod_params.show_states && !rid_cod_params.force_text_fields) {
                var selectedState = stateSelect.val();
                if (!selectedState) {
                    showMessage('يرجى اختيار الولاية', 'error');
                    stateSelect.focus();
                    return;
                }
            }
            
            // Validate variation selection if product has variations
            if (hasVariations) {
                var hasSelectedVariation = false;
                variationSelects.each(function() {
                    if ($(this).val()) {
                        hasSelectedVariation = true;
                        return false;
                    }
                });
                
                if (!hasSelectedVariation) {
                    showMessage('يرجى اختيار خصائص المنتج', 'error');
                    return;
                }
            }
            
            // Update button state
            thirdFormSubmitBtn.prop('disabled', true).text(rid_cod_params.processing || 'جاري المعالجة...');
            
            // Submit the form using the main form submission logic
            form.trigger('submit');
        });
        
        // Third form WhatsApp handler
        thirdFormWhatsAppBtn.on('click', function() {
            var fullName = $('#rid-cod-full-name').val().trim();
            var phone = $('#rid-cod-phone').val().trim();
            var address = $('#rid-cod-address').val().trim();
            var state = stateSelect.find('option:selected').text() || $('#rid-cod-state-text').val();
            var city = citySelect.find('option:selected').text() || $('#rid-cod-city-text').val();
            var notes = $('#rid-cod-notes').val().trim();
            var deliveryType = $('input[name="delivery_type"]:checked').val();
            var deliveryTypeText = deliveryType === 'home' ? 'للمنزل' : 'لمكتب التوصيل';
            
            // Get product info
            var productName = $('.product_title').text() || 'المنتج';
            var productPrice = thirdFormProductPrice.text();
            var shippingPrice = thirdFormShippingPrice.text();
            var totalPrice = thirdFormTotalPrice.text();
            
            // Build WhatsApp message
            var message = 'طلب جديد:\n\n';
            message += '👤 الاسم: ' + fullName + '\n';
            message += '📱 الهاتف: ' + phone + '\n';
            message += '📍 العنوان: ' + address + '\n';
            if (state) message += '🏛️ الولاية: ' + state + '\n';
            if (city) message += '🏘️ البلدية: ' + city + '\n';
            message += '🚚 نوع التوصيل: ' + deliveryTypeText + '\n';
            message += '\n📦 تفاصيل الطلب:\n';
            message += '🛍️ المنتج: ' + productName + '\n';
            message += '📊 الكمية: ' + currentQuantity + '\n';
            
            // Add variation details if available
            if (hasVariations) {
                var variationDetails = '';
                variationSelects.each(function() {
                    var $select = $(this);
                    var selectedText = $select.find('option:selected').text();
                    var label = $select.data('decoded-label') || $select.attr('data-attribute_name');
                    
                    if ($select.val() && selectedText) {
                        variationDetails += label + ': ' + selectedText + '\n';
                    }
                });
                if (variationDetails) {
                    message += variationDetails;
                }
            }
            
            message += '\n💰 الأسعار:\n';
            message += '🛍️ سعر المنتجات: ' + productPrice + '\n';
            message += '🚚 سعر التوصيل: ' + shippingPrice + '\n';
            message += '💳 المجموع: ' + totalPrice + '\n';
            
            if (notes) {
                message += '\n📝 ملاحظات: ' + notes + '\n';
            }
            
            // Open WhatsApp
            var whatsappNumber = rid_cod_params.whatsapp_number || '';
            if (whatsappNumber) {
                var whatsappUrl = 'https://wa.me/' + whatsappNumber + '?text=' + encodeURIComponent(message);
                window.open(whatsappUrl, '_blank');
            }
        });
        
        // Promo code button (placeholder functionality)
        $('.rid-cod-promo-btn').on('click', function() {
            showMessage('ميزة الرمز الترويجي قيد التطوير', 'error');
        });
    }